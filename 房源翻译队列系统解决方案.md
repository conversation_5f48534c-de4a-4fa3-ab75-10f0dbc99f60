# 房源翻译队列系统解决方案

## 需求分析

### 当前问题
- 房源prop.m翻译请求直接同步处理，可能造成性能瓶颈
- 无法根据优先级灵活选择翻译服务
- 缺乏对不同触发源的区分处理机制
- 没有有效的并发控制和资源管理

### 目标需求
1. 房源prop.m的翻译请求改用queue异步处理
2. 高优先级使用gemini，否则使用rm/ovh翻译服务
3. 根据自然import的数据源重要性区分处理优先级（暂时没有用户触发途径）
4. 实现pool size限制，控制并发处理数量
5. gemini需要立刻处理，rm/ovh可以延迟处理

## 现有架构分析

### 当前翻译系统
- **监听机制**: `watchPropAndTranslate.coffee`使用MongoDB change stream监听properties集合变化
- **翻译管理**: `translatorManager.coffee`管理多种翻译服务，支持使用限制和等待队列
- **翻译服务**: 支持gemini、rm、ovh等多种翻译服务，每个服务有maxUsage限制
- **处理方式**: 同步处理，直接调用翻译服务

### Queue参考实现
- **基础架构**: `mlsResourceDownloadQueue.coffee`提供了基于MongoDB的优先级队列实现
- **核心特性**: 支持优先级排序、批量处理、防重复处理机制
- **字段设计**: 使用dlShallEndTs字段防止重复处理，支持priority字段排序

## 解决方案设计

### 1. 架构重构：从Watch模式到Queue模式

#### 1.1 现有架构问题分析

**当前watchPropAndTranslate.coffee的问题**：
1. **效率低下**：监听整个properties集合的所有变化，包括大量不需要翻译的更新
2. **资源浪费**：Change Stream需要持续监听，消耗数据库连接和内存
3. **逻辑混乱**：翻译逻辑和数据变更监听耦合在一起
4. **扩展困难**：难以实现复杂的优先级和并发控制
5. **重复处理**：可能对同一房源进行多次不必要的翻译检查

#### 1.2 新架构设计：Queue-Based Translation System

**核心思想**：
- **取消watchPropAndTranslate.coffee**：不再监听properties集合变化
- **在数据源头添加队列**：在saveToMaster.coffee中精确识别需要翻译的情况
- **专用队列处理器**：新增propTranslationQueueProcessor.coffee专门处理翻译队列

**架构对比**：

**旧架构流程**：
Properties集合变化 → Change Stream监听 → watchPropAndTranslate.coffee处理 → 直接调用翻译服务

**新架构流程**：
saveToMaster.coffee数据处理 → 添加任务到PropTranslationQueue → propTranslationQueueProcessor.coffee监听队列 → 按优先级处理翻译服务

### 2. 翻译队列系统架构

#### 1.1 队列数据结构设计

**propTranslationQueue集合字段说明**：
- **_id**: 房源ID，直接使用房源ID作为队列任务ID
- **priority**: 优先级数值，1-10范围，10为最高优先级
- **triggerSource**: 触发源标识，当前仅支持'natural_import'（自然导入）
- **content**: 待翻译的房源描述内容
- **translationServices**: 可用翻译服务列表，如['gemini']或['rm', 'ovh']
- **status**: 任务状态，包括'pending'（待处理）、'processing'（处理中）、'completed'（已完成）、'failed'（失败）
- **createdAt**: 任务创建时间戳
- **processStartAt**: 开始处理时间戳
- **processEndAt**: 处理结束时间戳
- **retryCount**: 重试次数计数器
- **lastError**: 最后一次错误信息记录
- **dlShallEndTs**: 处理锁定时间，防止重复处理（参考mlsResourceDownloadQueue的设计）

#### 1.2 优先级策略设计

**高优先级数据源策略**：
- 优先级设置为3-4，确保重要数据源的及时处理
- 主要MLS系统（treb、bcre、ddf）使用此策略
- 活跃房源获得更高优先级

**中优先级数据源策略**：
- 优先级设置为2，适合区域性MLS系统
- 使用rm/ovh翻译服务，平衡成本和质量
- 支持批量处理，提高整体效率

**低优先级数据源策略**：
- 优先级设置为1，用于小型MLS系统
- 可以延迟处理，在系统负载较低时批量执行

**未来扩展预留**：
- 优先级5-10预留给未来的用户触发功能
- 支持动态优先级调整机制

### 2. 触发源识别机制

#### 2.1 当前阶段：仅支持自然触发

**重要说明**：在当前的架构设计中，暂时没有用户触发的途径。所有翻译请求都来自于MLS数据的自然导入过程。

#### 2.2 自然触发识别策略

##### 2.2.1 MLS导入源识别
现有系统有完善的MLS导入机制，所有翻译需求都来自于数据导入过程：

1. **导入进程标识**
   - watchAndImportToProperties.coffee负责MLS数据导入监听
   - mlsImport目录下的各种导入脚本处理不同数据源
   - 通过进程状态和导入标识可以准确识别数据来源

2. **数据源标识**
   - prop.src字段标识数据来源：TRB、DDF、BRE、RHB、EDM、CAR、OTW等
   - 不同数据源有不同的导入模式和频率
   - 可以根据数据源重要性设置不同的翻译优先级

### 3. 翻译服务选择策略

#### 3.1 服务分配规则设计

**高优先级数据源的服务策略**：
- 优先级设置为3-4，确保重要数据的快速处理
- 优先使用rm翻译服务，必要时可使用gemini
- 最大重试次数设置为2次，平衡质量和效率
- 超时时间设置为60秒，允许充分的处理时间

**中低优先级数据源的服务策略**：
- 优先级设置为1-2，适合批量处理
- 主要使用rm和ovh翻译服务，注重成本效益
- 最大重试次数设置为1次，避免过度重试影响效率
- 超时时间设置为2分钟，允许更长的处理时间

**服务选择的核心原则**：
- 高优先级数据源优先质量和速度
- 低优先级数据源优先成本效益，质量适中即可
- 根据数据源重要性自动匹配最合适的翻译服务
- 预留gemini服务用于未来的高优先级需求

#### 3.2 服务降级机制设计

**自动降级策略**：
- 当gemini服务不可用或响应超时时，自动降级到rm或ovh服务
- 保证高优先级的翻译请求即使在主要服务故障时也能得到处理
- 降级过程自动进行，不影响整体处理流程

**全服务故障处理**：
- 当所有翻译服务都不可用时，任务自动加入重试队列
- 设置合理的重试间隔，避免频繁重试加重服务负担
- 记录故障信息，便于后续问题排查和服务恢复

**失败任务管理**：
- 重试次数超过限制后，任务标记为失败状态
- 保留失败任务的详细错误信息和上下文
- 支持手动重新处理失败任务的机制

### 4. 并发控制和Pool Size限制详细设计

#### 4.1 Pool Size配置策略

基于现有翻译服务的特性和限制，制定详细的Pool Size配置：

##### 4.1.1 翻译服务特性分析

1. **Gemini服务特性**
   - 高质量翻译，适合高优先级请求
   - API限制：通常有较严格的QPS限制
   - 成本较高，需要精确控制使用量
   - 响应时间：通常1-3秒

2. **RM服务特性**
   - 自建服务，成本可控
   - 可承受较高并发
   - 响应时间：通常2-5秒
   - 适合批量处理

3. **OVH服务特性**
   - 第三方服务，成本中等
   - 稳定性较好
   - 响应时间：通常3-8秒
   - 适合中等优先级任务

##### 4.1.2 详细Pool Size配置策略

**Gemini服务配置策略**：
- 最大并发数设置为3，采用保守策略避免触发API限制
- 队列容量设置为50，适合高优先级任务的快速处理
- 速率限制：每分钟60次请求，每小时1000次请求
- 超时设置：30秒请求超时，5秒重试延迟，最多重试2次
- 优先级要求：最低优先级7，确保只处理重要任务
- 保留槽位：为最高优先级任务保留1个处理槽位
- 成本控制：每日使用限制5000次，单次成本0.01元

**RM服务配置策略**：
- 最大并发数设置为15，充分利用自建服务的处理能力
- 队列容量设置为1000，支持大批量任务处理
- 速率限制：每分钟300次请求，每小时10000次请求
- 超时设置：60秒请求超时，3秒重试延迟，最多重试3次
- 优先级要求：接受所有优先级任务，最低优先级1
- 保留槽位：为高优先级任务保留3个处理槽位
- 健康检查：30秒间隔检查，最多允许5次连续失败

**OVH服务配置策略**：
- 最大并发数设置为8，平衡处理能力和稳定性
- 队列容量设置为500，适合中等规模任务处理
- 速率限制：每分钟120次请求，每小时5000次请求
- 超时设置：45秒请求超时，4秒重试延迟，最多重试2次
- 优先级要求：最低优先级2，过滤掉最低优先级任务
- 保留槽位：为高优先级任务保留2个处理槽位
- 成本控制：每日使用限制8000次，单次成本0.005元

### 5. 系统组件设计

#### 5.1 新增：propTranslationQueueProcessor.coffee

这是替代watchPropAndTranslate.coffee的新文件，专门处理翻译队列：

**核心功能设计**：
- 基于优先级的队列调度处理
- 支持多种翻译服务的智能选择
- 实现并发控制和资源管理
- 提供完善的错误处理和重试机制

**主要组件说明**：

**初始化和配置**：
- 加载所有翻译服务配置（azure、deepseek、deepL、openAI、gemini、claude、grok、rm、ovh）
- 设置处理参数：批量大小20个任务，处理间隔5秒
- 初始化翻译管理器和动态Pool管理器

**任务获取逻辑**：
- 按优先级降序和创建时间升序排序获取待处理任务
- 使用锁定机制防止重复处理，锁定时间10分钟
- 支持批量获取，提高处理效率

**翻译处理流程**：
- 根据任务优先级和服务可用性选择最佳翻译服务
- 通过信号量机制控制并发数，避免服务过载
- 执行翻译后更新房源的m_zh字段
- 记录处理结果和使用的翻译服务

**错误处理和重试**：
- 翻译失败时根据重试次数决定是否重试
- 超过最大重试次数的任务标记为失败状态
- 记录详细的错误信息便于问题排查

**主处理循环**：
- 定期从队列获取任务进行批量处理
- 并行处理多个翻译任务，提高整体效率
- 实时统计处理速度和成功率